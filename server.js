require('dotenv').config();

// Setup axios interceptors IMMEDIATELY, before any other modules
if (process.env.NODE_ENV === 'development' || process.env.ENABLE_DEBUG_LOGGING === 'true') {
  const { setupAxiosInterceptors } = require('./server/middleware/debugLogging');
  // We'll pass the websocket server later, but set up the interceptors now
  setupAxiosInterceptors(null);
}

const express = require('express');
const mongoose = require('mongoose');
const session = require('express-session');
const MongoStore = require('connect-mongo');
const passport = require('passport');
const cors = require('cors');
const helmet = require('helmet');
const path = require('path');

// Initialize Express app
const app = express();

// Security middleware
app.use(
  helmet({
    contentSecurityPolicy: {
      directives: {
        ...helmet.contentSecurityPolicy.getDefaultDirectives(),
        "img-src": ["'self'", "data:", "https://lh3.googleusercontent.com", "https://mosaic.scdn.co", "https://i.scdn.co", "http://172.18.0.71/cgi-bin/mjpeg?resolution=640x360&framerate=30&quality=1"],
        "frame-src": ["'self'", "https://drive.google.com", "https://docs.google.com", "https://docs.google.com/forms", "https://forms.gle"]
      }
    },
    crossOriginEmbedderPolicy: false, // Disable COEP to allow loading cross-origin resources
    crossOriginOpenerPolicy: { policy: "same-origin-allow-popups" } // Allow popups from cross-origin resources
  })
);
app.use(cors({
  origin: true, // Allow any origin that sends credentials
  credentials: true // Allow cookies to be sent with requests
}));

// Add Cross-Origin-Resource-Policy header to allow cross-origin resources
app.use((req, res, next) => {
  res.setHeader('Cross-Origin-Resource-Policy', 'cross-origin');
  next();
});

// Body parser middleware
app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Debug logging middleware (only in development or when explicitly enabled)
if (process.env.NODE_ENV === 'development' || process.env.ENABLE_DEBUG_LOGGING === 'true') {
  const { createDebugLogger, setWebsocketServer } = require('./server/middleware/debugLogging');
  const websocketServer = require('./server/websocket/websocketServer');
  
  // Set the websocket server reference for the already-setup axios interceptors
  setWebsocketServer(websocketServer);
  
  // Setup middleware to capture internal API calls
  app.use(createDebugLogger(websocketServer));
}

// Configure session storage
app.use(
  session({
    secret: process.env.SESSION_SECRET || 'keyboard cat',
    resave: true,
    saveUninitialized: false,
    store: MongoStore.create({
      mongoUrl: process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal',
      collectionName: 'sessions',
      autoRemove: 'native', // Use MongoDB's TTL index
      touchAfter: 24 * 3600, // time period in seconds to update the session
      crypto: {
        secret: process.env.SESSION_SECRET || 'keyboard cat'
      }
    }),
    cookie: {
      maxAge: 1000 * 60 * 60 * 24, // 1 day
      secure: process.env.NODE_ENV === 'production' && process.env.SECURE_COOKIES === 'true',
      sameSite: 'lax', // Allow cookies to be sent in same-site requests
      httpOnly: true // Prevent client-side JS from reading the cookie
    }
  })
);

// Passport middleware
app.use(passport.initialize());
app.use(passport.session());

// Passport config
require('./config/passport')(passport);

// Connect to MongoDB
mongoose
  .connect(process.env.MONGO_URI || 'mongodb://localhost:27017/csfportal', {
    useNewUrlParser: true,
    useUnifiedTopology: true
  })
  .then(async () => {
    console.log('MongoDB Connected');

    // Initialize default roles
    const Role = require('./models/Role');
    try {
      await Role.createDefaultRoles();
      console.log('Default roles created');
    } catch (err) {
      console.error('Error creating default roles:', err);
    }

    // Start RADIUS server
    try {
      const radiusServer = require('./server/integrations/radius/radiusServer');
      await radiusServer.start();
      console.log('RADIUS server started');
    } catch (err) {
      console.error('Error starting RADIUS server:', err);
    }

    // Initialize Gmail webhook listener
    try {
      const gmailWebhookController = require('./server/controllers/gmailWebhookController');
      await gmailWebhookController.initialize();
      console.log('Gmail webhook listener initialized');
    } catch (err) {
      console.error('Error initializing Gmail webhook:', err);
    }

    // Initialize Contact Sync Scheduler
    try {
      const contactSyncScheduler = require('./server/utils/contactSyncScheduler');
      await contactSyncScheduler.initialize();
      console.log('Contact Sync Scheduler initialized');
    } catch (err) {
      console.error('Error initializing Contact Sync Scheduler:', err);
    }

    // Initialize Building Management Controller
    try {
      const buildingManagementController = require('./server/controllers/buildingManagementController');
      await buildingManagementController.initialize();
      console.log('Building Management Controller initialized');
    } catch (err) {
      console.error('Error initializing Building Management Controller:', err);
    }

    // Initialize Google User Auto Sync Scheduler
    try {
      const googleUserSyncScheduler = require('./server/utils/googleUserSyncScheduler');
      await googleUserSyncScheduler.initialize();
      console.log('Google User Sync Scheduler initialized');
    } catch (err) {
      console.error('Error initializing Google User Sync Scheduler:', err);
    }
  })
  .catch(err => console.log(err));

// Define Routes
app.use('/api/auth', require('./routes/api/auth'));
app.use('/api/users', require('./routes/api/users'));
app.use('/api/roles', require('./routes/api/roles'));
app.use('/api/shortcuts', require('./routes/api/shortcuts'));
app.use('/api/google', require('./routes/api/google'));
app.use('/api/google-drive', require('./routes/api/googleDrive'));
app.use('/api/planning-center', require('./routes/api/planningCenter'));
app.use('/api/synology', require('./routes/api/synology'));
app.use('/api/canva', require('./routes/api/canva'));
app.use('/api/mosyle-business', require('./routes/api/mosyleBusiness'));
app.use('/api/dreo', require('./routes/api/dreo'));
app.use('/api/lg-thinq', require('./routes/api/lgThinq'));
app.use('/api/unifi-protect', require('./routes/api/unifiProtect'));
app.use('/api/unifi-access', require('./routes/api/unifiAccess'));
app.use('/api/unifi-network', require('./routes/api/unifiNetwork'));
app.use('/api/lenel-s2-netbox', require('./routes/api/lenelS2NetBox'));
app.use('/api/access-control', require('./routes/api/accessControl'));
app.use('/api/rain-bird', require('./routes/api/rainBird'));
app.use('/api/help/entries', require('./routes/api/helpEntries'));
app.use('/api/help/categories', require('./routes/api/helpCategories'));
app.use('/api/asset-requests', require('./routes/api/assetRequests'));
app.use('/api/asset-issues', require('./routes/api/assetIssues'));
app.use('/api/asset-reports', require('./routes/api/assetReports'));
app.use('/api/assets', require('./routes/api/assets'));
app.use('/api/tasks', require('./routes/api/tasks'));
app.use('/api/shortcut-categories', require('./routes/api/shortcutCategories'));
app.use('/api/menu-categories', require('./routes/api/menuCategories'));
app.use('/api/menu-items', require('./routes/api/menuItems'));
app.use('/api/staff-directory', require('./routes/api/staffDirectory'));
app.use('/api/google-admin', require('./routes/api/googleAdmin'));
app.use('/api/google-calendar', require('./routes/api/googleCalendar'));
app.use('/api/google-forms', require('./routes/api/googleForms'));
app.use('/api/google-forms-webhooks', require('./routes/api/googleFormsWebhooks'));
app.use('/api/people', require('./routes/api/people'));
app.use('/api/dashboard-preferences', require('./routes/api/dashboardPreferences'));
app.use('/api/apple-business-manager', require('./routes/api/appleBusinessManager'));
app.use('/api/radius', require('./server/routes/api/radius'));
app.use('/api/wiim', require('./routes/api/wiim'));
app.use('/api/skyportcloud', require('./routes/skyportCloudRoutes'));
app.use('/api/integration-status', require('./routes/api/integrationStatus'));
app.use('/api/integrations', require('./routes/api/integrations'));
app.use('/api/user-provisioning', require('./routes/api/userProvisioning'));
app.use('/api/search', require('./routes/api/search'));
app.use('/api/debug', require('./routes/api/debug'));
app.use('/api/qsys', require('./routes/api/qsys'));
app.use('/api/zeevee', require('./routes/api/zeeVee'));
app.use('/api/colorlit', require('./routes/api/colorlit'));
app.use('/api/panasonic', require('./routes/api/panasonic'));
app.use('/api/govee', require('./routes/api/govee'));
app.use('/api/constant-contact', require('./routes/api/constantContact'));
app.use('/api/notes', require('./routes/api/notes'));
app.use('/api/notifications', require('./routes/api/notifications'));
app.use('/api/news-categories', require('./routes/api/newsCategories'));
app.use('/api/news-posts', require('./routes/api/newsPosts'));
app.use('/api/team-hub', require('./routes/api/teamHub'));

// Form System routes
app.use('/api/forms', require('./server/routes/api/forms'));
app.use('/api/public-forms', require('./server/routes/api/publicForms'));

// Ticketing System routes
app.use('/api/tickets', require('./server/routes/ticketRoutes'));
app.use('/api/ticket-categories', require('./server/routes/ticketCategoryRoutes'));
app.use('/api/ticket-tags', require('./server/routes/ticketTagRoutes'));
app.use('/api/ticket-email', require('./server/routes/ticketEmailRoutes'));
app.use('/api/ticket-forms-webhooks', require('./server/routes/ticketFormsWebhookRoutes'));

// Building Management System routes
app.use('/api/buildings', require('./server/routes/buildingRoutes'));
app.use('/api/floors', require('./server/routes/floorRoutes'));
app.use('/api/floorplan-icons', require('./server/routes/floorplanIconRoutes'));
app.use('/api/rooms', require('./server/routes/roomRoutes'));
app.use('/api/reservations', require('./server/routes/reservationRoutes'));
app.use('/api/building-management', require('./server/routes/api/buildingManagement'));
app.use('/api/building-tracking', require('./server/routes/buildingTrackingRoutes'));
app.use('/api/contacts', require('./server/routes/contactRoutes'));
app.use('/api/contacts/sync', require('./server/routes/contactSyncRoutes'));
app.use('/api/electrical', require('./server/routes/electricalRoutes'));
app.use('/api/safety', require('./server/routes/safetyRoutes'));
app.use('/api/hvac', require('./server/routes/hvacRoutes'));
app.use('/api/wifi', require('./server/routes/wifiRoutes'));
app.use('/api/utility-shutoffs', require('./server/routes/utilityShutoffRoutes'));
app.use('/api/church-events', require('./server/routes/churchEventRoutes'));
app.use('/api/av-equipment', require('./server/routes/avEquipmentRoutes'));
app.use('/api/event-mode-presets', require('./server/routes/eventModePresetRoutes'));
app.use('/api/volunteer-resources', require('./server/routes/volunteerResourceRoutes'));
app.use('/api/av-checklists', require('./server/routes/avChecklistRoutes'));
app.use('/api/wayfinding', require('./server/routes/wayfindingRoutes'));
app.use('/api/coverage', require('./server/routes/coverageRoutes'));

// Messaging System routes
app.use('/api/messages', require('./server/routes/messageRoutes'));

// Serve static assets in production
if (process.env.NODE_ENV === 'production') {
  // Set static folder
  app.use(express.static('client/build'));

  app.get('*', (req, res) => {
    res.sendFile(path.resolve(__dirname, 'client', 'build', 'index.html'));
  });
}

const PORT = process.env.PORT || 6000;

// Create HTTP server
const http = require('http');
const server = http.createServer(app);

// Initialize WebSocket server
const websocketServer = require('./server/websocket/websocketServer');
const realtimeService = require('./server/services/realtimeService');
const websocketService = require('./server/services/websocketService');

websocketServer.initialize(server);
websocketService.initialize(websocketServer);

// Start realtime monitoring service
realtimeService.start();

// Graceful shutdown
process.on('SIGTERM', () => {
  console.log('SIGTERM received, shutting down gracefully');
  realtimeService.stop();
  websocketServer.shutdown();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

process.on('SIGINT', () => {
  console.log('SIGINT received, shutting down gracefully');
  realtimeService.stop();
  websocketServer.shutdown();
  server.close(() => {
    console.log('Server closed');
    process.exit(0);
  });
});

server.listen(PORT, () => {
  console.log(`Server started on port ${PORT}`);
  console.log(`WebSocket server available at ws://localhost:${PORT}/ws`);
});
