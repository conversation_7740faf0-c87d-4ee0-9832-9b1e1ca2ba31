import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box } from '@mui/material';
import { useAuth } from './context/AuthContext';
import { DashboardProvider } from './context/DashboardContext';
import { DebugProvider } from './context/DebugContext';

// Task Management
import TasksPage from './pages/Tasks/TasksPage';
import TaskFormPage from './pages/Tasks/TaskFormPage';
import MaintenanceTaskFormPage from './pages/Tasks/MaintenanceTaskFormPage';
import TaskDetailPage from './pages/Tasks/TaskDetailPage';

// Ticket Management
import TicketsPage from './pages/Tickets/TicketsPage';
import TicketDetailPage from './pages/Tickets/TicketDetailPage';
import TicketFormPage from './pages/Tickets/TicketFormPage';
import TicketCategoriesAndTagsPage from './pages/Tickets/TicketCategoriesAndTagsPage';

// Layout components
import Layout from './components/Layout';

// Page components
import HomePage from './pages/HomePage';
import LoginPage from './pages/LoginPage';
import DashboardPage from './pages/DashboardPage';
import ShortcutsPage from './pages/ShortcutsPage';
import DriveFilesPage from './pages/DriveFilesPage';
import DriveViewerPage from './pages/DriveViewerPage';
import AdminUsersPage from './pages/AdminUsersPage';
import AdminRolesPage from './pages/AdminRolesPage';
import AdminShortcutsPage from './pages/AdminShortcutsPage';
import ProvisioningManagementPage from './pages/Admin/ProvisioningManagementPage';
import AdminMenuPage from './pages/Admin/AdminMenuPage';
import SystemStatusPage from './pages/Admin/SystemStatusPage';
import NotFoundPage from './pages/NotFoundPage';

// Help and FAQ pages
import HelpPage from './pages/Help/HelpPage';
import FAQPage from './pages/Help/FAQPage';

// Getting Started help pages
import LoggingInPage from './pages/Help/GettingStarted/LoggingInPage';
import NavigationPage from './pages/Help/GettingStarted/NavigationPage';
import YourProfilePage from './pages/Help/GettingStarted/YourProfilePage';

// Core Features help pages
import DashboardHelpPage from './pages/Help/CoreFeatures/DashboardPage';
import ShortcutsHelpPage from './pages/Help/CoreFeatures/ShortcutsPage';
import DriveFilesHelpPage from './pages/Help/CoreFeatures/DriveFilesPage';
import CalendarHelpPage from './pages/Help/CoreFeatures/CalendarPage';
import StaffDirectoryHelpPage from './pages/Help/CoreFeatures/StaffDirectoryPage';

// Integrations help pages
import CanvaHelpPage from './pages/Help/Integrations/CanvaPage';
import PlanningCenterHelpPage from './pages/Help/Integrations/PlanningCenterPage';
import SynologyHelpPage from './pages/Help/Integrations/SynologyPage';
import DreoHelpPage from './pages/Help/Integrations/DreoPage';
import UnifiAccessHelpPage from './pages/Help/Integrations/UnifiAccessPage';
import GoogleDriveHelpPage from './pages/Help/Integrations/GoogleDrivePage';
import GoogleCalendarHelpPage from './pages/Help/Integrations/GoogleCalendarPage';
import GoogleFormsHelpPage from './pages/Help/Integrations/GoogleFormsPage';
import GoogleAdminHelpPage from './pages/Help/Integrations/GoogleAdminPage';
import LenelS2NetBoxHelpPage from './pages/Help/Integrations/LenelS2NetBoxPage';
import MosyleBusinessHelpPage from './pages/Help/Integrations/MosyleBusinessPage';
import UnifiNetworkHelpPage from './pages/Help/Integrations/UnifiNetworkPage';
import UnifiProtectHelpPage from './pages/Help/Integrations/UnifiProtectPage';
import RadiusHelpPage from './pages/Help/Integrations/RadiusPage';

// Staff Directory pages
import StaffDirectoryPage from './pages/StaffDirectory/StaffDirectoryPage';
import UserProfilePage from './pages/StaffDirectory/UserProfilePage';
import TeamManagementPage from './pages/StaffDirectory/TeamManagementPage';
import GroupManagementPage from './pages/StaffDirectory/GroupManagementPage';

// Integration pages
import CanvaPage from './pages/Canva/CanvaPage';
import CanvaSetup from './pages/Canva/CanvaSetup';
import PlanningCenterPage from './pages/PlanningCenter/PlanningCenterPage';
import SynologyFileBrowser from './pages/Synology/SynologyFileBrowser';
import SynologySetup from './pages/Synology/SynologySetup';

// New integration pages
import DreoPage from './pages/Dreo/DreoPage';
import LGThinqPage from './pages/LGThinq/LGThinqPage';
import RainBirdPage from './pages/RainBird/RainBirdPage';
import RainBirdSetup from './pages/RainBird/RainBirdSetup';


// Placeholder imports for components to be created
import LenelS2NetBoxPage from './pages/LenelS2NetBox/LenelS2NetBoxPage';
import TeamHubPage from './pages/TeamHub/TeamHubPage';
import TeamHubsIndexPage from './pages/TeamHub/TeamHubsIndexPage';
import MosyleBusinessPage from './pages/MosyleBusiness/MosyleBusinessPage';
import MosyleBusinessSetup from './pages/MosyleBusiness/MosyleBusinessSetup';
import UnifiAccessPage from './pages/UnifiAccess/UnifiAccessPage';
import UnifiedPeoplePage from './pages/People/UnifiedPeoplePage';
import GoogleAdminPage from './pages/GoogleAdmin/GoogleAdminPage';
import GoogleAdminUsersPage from './pages/GoogleAdmin/GoogleAdminUsersPage';
import GoogleAdminGroupsPage from './pages/GoogleAdmin/GoogleAdminGroupsPage';
import GoogleCalendarPage from './pages/GoogleCalendar/GoogleCalendarPage';
import GoogleFormsPage from './pages/GoogleForms/GoogleFormsPage';
import GoogleFormsWebhookPage from './pages/GoogleForms/Webhooks/GoogleFormsWebhookPage';
import GoogleConfigEnvMessage from './pages/GoogleConfigEnvMessage';
import UnifiNetworkPage from './pages/UnifiNetwork/UnifiNetworkPage';
import UnifiProtectPage from './pages/UnifiProtect/UnifiProtectPage';
import AppleBusinessManagerPage from './pages/AppleBusinessManager/AppleBusinessManagerPage';
import BuildingManagementPage from './pages/BuildingManagement/BuildingManagementPage';
import RoomBookingPage from './pages/RoomBooking/RoomBookingPage';
import BuildingManagementSetup from './pages/BuildingManagement/BuildingManagementSetup';
import BuildingManagementAdminPage from './pages/Admin/BuildingManagementAdminPage';
import PhoneBookPage from './pages/PhoneBook/PhoneBookPage';
import RadiusPage from './pages/Radius/RadiusPage';
import RadiusSetup from './pages/Radius/RadiusSetup';
import WiimPage from './pages/Wiim/WiimPage';
import AccessControlPage from './pages/AccessControl/AccessControlPage';
import WiimSetup from './pages/Wiim/WiimSetup';
import SkyportCloudPage from './pages/SkyportCloud/SkyportCloudPage';
import QsysPage from './pages/Qsys/QsysPage';
import ColoritPage from './pages/Colorlit/ColoritPage';
import ZeeVeePage from './pages/ZeeVee/ZeeVeePage';
import PanasonicPage from './pages/Panasonic/PanasonicPage';
import GoveePage from './pages/Govee/GoveePage';
import ConstantContactPage from './pages/ConstantContact/ConstantContactPage';

// BMS imports
import BMSPage from './pages/BMS/BMSPage';

// Form Builder imports
import FormBuilderPage from './pages/Forms/FormBuilderPage';
import FormsListPage from './pages/Forms/FormsListPage';
import FormViewPage from './pages/Forms/FormViewPage';

// Asset Management pages
import AssetManagementPage from './pages/AssetManagement/AssetManagementPage';
import AssetRequestsPage from './pages/AssetManagement/AssetRequestsPage';
import AssetIssuesPage from './pages/AssetManagement/AssetIssuesPage';
import AssetReportsPage from './pages/Assets/AssetReportsPage';
import AssetImportExportPage from './pages/Assets/AssetImportExportPage';
import AssetListPage from './pages/Assets/AssetListPage';
import AssetFormPage from './pages/Assets/AssetFormPage';
import AssetDashboard from './pages/Assets/AssetDashboard';

// Notes page
import NotesPage from './pages/Notes/NotesPage';

// News pages
import NewsPage from './pages/News/NewsPage';
import NewsPostPage from './pages/News/NewsPostPage';
import NewsPostForm from './pages/News/NewsPostForm';

// Debug widget
import DebugWidget from './components/DebugWidget/DebugWidget';

// Protected route component
const ProtectedRoute = ({ children, requiredRoles = [] }) => {
  const { user, loading } = useAuth();

  // Show loading indicator while checking authentication
  if (loading) {
    return <Box sx={{ p: 3 }}>Loading...</Box>;
  }

  // If not authenticated, redirect to login
  if (!user) {
    return <Navigate to="/login" />;
  }

  // If roles are required, check if user has at least one of them
  if (requiredRoles.length > 0) {
    const hasRequiredRole = requiredRoles.some(role => user.roles.includes(role));
    if (!hasRequiredRole && !user.roles.includes('admin')) {
      return <Navigate to="/dashboard" />;
    }
  }

  return children;
};

function App() {
  return (
    <DebugProvider>
      <Routes>
        <Route path="/" element={<Layout />}>
        {/* Public routes */}
        <Route index element={<HomePage />} />
        <Route path="login" element={<LoginPage />} />

        {/* Protected routes */}
        <Route 
          path="dashboard" 
          element={
            <ProtectedRoute>
              <DashboardProvider>
                <DashboardPage />
              </DashboardProvider>
            </ProtectedRoute>
          } 
        />

        <Route 
          path="shortcuts" 
          element={
            <ProtectedRoute>
              <ShortcutsPage />
            </ProtectedRoute>
          } 
        />

        {/* Apps route removed - now using modal in header */}

        <Route 
          path="drive" 
          element={
            <ProtectedRoute>
              <DriveFilesPage />
            </ProtectedRoute>
          } 
        />

        <Route 
          path="drive/view/:fileId" 
          element={
            <ProtectedRoute>
              <DriveViewerPage />
            </ProtectedRoute>
          } 
        />

        <Route 
          path="room-booking" 
          element={
            <ProtectedRoute>
              <RoomBookingPage />
            </ProtectedRoute>
          } 
        />

        {/* Admin routes */}
        <Route 
          path="admin/users" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <AdminUsersPage />
            </ProtectedRoute>
          } 
        />

        <Route 
          path="admin/roles" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <AdminRolesPage />
            </ProtectedRoute>
          } 
        />

        <Route 
          path="admin/shortcuts" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <AdminShortcutsPage />
            </ProtectedRoute>
          } 
        />

        <Route 
          path="admin/menu" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <AdminMenuPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="admin/status" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <SystemStatusPage />
            </ProtectedRoute>
          } 
        />


        <Route 
          path="admin/building-management" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <BuildingManagementAdminPage />
            </ProtectedRoute>
          } 
        />

        <Route 
          path="admin/provisioning" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <ProvisioningManagementPage />
            </ProtectedRoute>
          } 
        />

        {/* Integration routes */}
        <Route 
          path="canva" 
          element={
            <ProtectedRoute>
              <CanvaPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="canva/setup" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <CanvaSetup />
            </ProtectedRoute>
          } 
        />



        <Route 
          path="planning-center/*" 
          element={
            <ProtectedRoute>
              <PlanningCenterPage />
            </ProtectedRoute>
          } 
        />

        <Route 
          path="synology" 
          element={
            <ProtectedRoute>
              <SynologyFileBrowser />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="synology/setup" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <SynologySetup />
            </ProtectedRoute>
          } 
        />

        {/* Dreo routes */}
        <Route 
          path="dreo" 
          element={
            <ProtectedRoute>
              <DreoPage />
            </ProtectedRoute>
          } 
        />

        {/* LG ThinQ routes */}
        <Route 
          path="lg-thinq" 
          element={
            <ProtectedRoute>
              <LGThinqPage />
            </ProtectedRoute>
          } 
        />

        {/* Rain Bird routes */}
        <Route 
          path="rain-bird" 
          element={
            <ProtectedRoute>
              <RainBirdPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="rain-bird/setup" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <RainBirdSetup />
            </ProtectedRoute>
          } 
        />

        {/* Lenel S2 NetBox routes */}
        <Route 
          path="lenel-s2-netbox" 
          element={
            <ProtectedRoute>
              <LenelS2NetBoxPage />
            </ProtectedRoute>
          } 
        />

        {/* Mosyle Business routes */}
        <Route 
          path="mosyle-business" 
          element={
            <ProtectedRoute>
              <MosyleBusinessPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="mosyle-business/setup" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <MosyleBusinessSetup />
            </ProtectedRoute>
          } 
        />

        {/* UniFi Access routes */}
        <Route 
          path="unifi-access" 
          element={
            <ProtectedRoute>
              <UnifiAccessPage />
            </ProtectedRoute>
          } 
        />

        {/* UniFi Network routes */}
        <Route 
          path="unifi-network" 
          element={
            <ProtectedRoute>
              <UnifiNetworkPage />
            </ProtectedRoute>
          } 
        />

        {/* UniFi Protect routes */}
        <Route 
          path="unifi-protect" 
          element={
            <ProtectedRoute>
              <UnifiProtectPage />
            </ProtectedRoute>
          } 
        />

        {/* Access Control routes */}
        <Route 
          path="access-control" 
          element={
            <ProtectedRoute>
              <AccessControlPage />
            </ProtectedRoute>
          } 
        />

        {/* Google Admin routes */}
        <Route 
          path="google-admin" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <GoogleAdminPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="google-admin/config" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <GoogleConfigEnvMessage serviceName="Google Admin" />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="google-admin/users" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <GoogleAdminUsersPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="google-admin/groups" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <GoogleAdminGroupsPage />
            </ProtectedRoute>
          } 
        />

        {/* Google Calendar routes */}
        <Route 
          path="google-calendar" 
          element={
            <ProtectedRoute>
              <GoogleCalendarPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="google-calendar/config" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <GoogleConfigEnvMessage serviceName="Google Calendar" />
            </ProtectedRoute>
          } 
        />

        {/* Google Forms routes */}
        <Route 
          path="google-forms" 
          element={
            <ProtectedRoute>
              <GoogleFormsPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="google-forms/config" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <GoogleConfigEnvMessage serviceName="Google Forms" />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="google-forms/webhooks/create" 
          element={
            <ProtectedRoute>
              <GoogleFormsWebhookPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="google-forms/webhooks/:id" 
          element={
            <ProtectedRoute>
              <GoogleFormsWebhookPage />
            </ProtectedRoute>
          } 
        />


        {/* Apple Business Manager routes */}
        <Route 
          path="apple-business-manager" 
          element={
            <ProtectedRoute>
              <AppleBusinessManagerPage />
            </ProtectedRoute>
          } 
        />

        {/* Building Management routes */}
        <Route 
          path="building-management/*" 
          element={
            <ProtectedRoute>
              <BuildingManagementPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="building-management/setup" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <BuildingManagementSetup />
            </ProtectedRoute>
          } 
        />

        {/* BMS (Building Management System) routes */}
        <Route 
          path="bms/*" 
          element={
            <ProtectedRoute>
              <BMSPage />
            </ProtectedRoute>
          } 
        />
        
        {/* Phone Book route */}
        <Route 
          path="phone-book" 
          element={
            <ProtectedRoute>
              <PhoneBookPage />
            </ProtectedRoute>
          } 
        />

        {/* RADIUS Server routes */}
        <Route 
          path="radius" 
          element={
            <ProtectedRoute>
              <RadiusPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="radius/setup" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <RadiusSetup />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="admin/radius" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <RadiusPage />
            </ProtectedRoute>
          } 
        />

        {/* WiiM Media Hub routes */}
        <Route 
          path="wiim/*" 
          element={
            <ProtectedRoute>
              <WiimPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="wiim/setup" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <WiimSetup />
            </ProtectedRoute>
          } 
        />

        {/* SkyportCloud HVAC Controls route */}
        <Route 
          path="skyportcloud" 
          element={
            <ProtectedRoute>
              <SkyportCloudPage />
            </ProtectedRoute>
          } 
        />

        {/* Q-sys route */}
        <Route 
          path="qsys" 
          element={
            <ProtectedRoute>
              <QsysPage />
            </ProtectedRoute>
          } 
        />

        {/* Colorlit route */}
        <Route 
          path="colorlit" 
          element={
            <ProtectedRoute>
              <ColoritPage />
            </ProtectedRoute>
          } 
        />

        {/* ZeeVee route */}
        <Route 
          path="zeevee" 
          element={
            <ProtectedRoute>
              <ZeeVeePage />
            </ProtectedRoute>
          } 
        />

        {/* Panasonic Pro AV route */}
        <Route 
          path="panasonic" 
          element={
            <ProtectedRoute>
              <PanasonicPage />
            </ProtectedRoute>
          } 
        />

        {/* Govee Smart Devices route */}
        <Route 
          path="govee" 
          element={
            <ProtectedRoute>
              <GoveePage />
            </ProtectedRoute>
          } 
        />

        {/* Constant Contact route */}
        <Route 
          path="constant-contact" 
          element={
            <ProtectedRoute>
              <ConstantContactPage />
            </ProtectedRoute>
          } 
        />

        {/* People Directory routes */}
        <Route 
          path="people/*" 
          element={
            <ProtectedRoute>
              <UnifiedPeoplePage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="team-hubs" 
          element={
            <ProtectedRoute>
              <TeamHubsIndexPage />
            </ProtectedRoute>
          } 
        />

        {/* Help and FAQ routes */}
        <Route 
          path="help" 
          element={
            <ProtectedRoute>
              <HelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/faq" 
          element={
            <ProtectedRoute>
              <FAQPage />
            </ProtectedRoute>
          } 
        />

        {/* Getting Started help routes */}
        <Route 
          path="help/logging-in" 
          element={
            <ProtectedRoute>
              <LoggingInPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/navigation" 
          element={
            <ProtectedRoute>
              <NavigationPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/your-profile" 
          element={
            <ProtectedRoute>
              <YourProfilePage />
            </ProtectedRoute>
          } 
        />

        {/* Core Features help routes */}
        <Route 
          path="help/dashboard" 
          element={
            <ProtectedRoute>
              <DashboardHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/shortcuts" 
          element={
            <ProtectedRoute>
              <ShortcutsHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/drive-files" 
          element={
            <ProtectedRoute>
              <DriveFilesHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/calendar" 
          element={
            <ProtectedRoute>
              <CalendarHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/staff-directory" 
          element={
            <ProtectedRoute>
              <StaffDirectoryHelpPage />
            </ProtectedRoute>
          } 
        />

        {/* Integrations help routes */}
        <Route 
          path="help/canva" 
          element={
            <ProtectedRoute>
              <CanvaHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/planning-center" 
          element={
            <ProtectedRoute>
              <PlanningCenterHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/synology" 
          element={
            <ProtectedRoute>
              <SynologyHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/dreo" 
          element={
            <ProtectedRoute>
              <DreoHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/unifi-access" 
          element={
            <ProtectedRoute>
              <UnifiAccessHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/google-drive" 
          element={
            <ProtectedRoute>
              <GoogleDriveHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/google-calendar" 
          element={
            <ProtectedRoute>
              <GoogleCalendarHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/google-forms" 
          element={
            <ProtectedRoute>
              <GoogleFormsHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/google-admin" 
          element={
            <ProtectedRoute>
              <GoogleAdminHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/lenel-s2-netbox" 
          element={
            <ProtectedRoute>
              <LenelS2NetBoxHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/mosyle-business" 
          element={
            <ProtectedRoute>
              <MosyleBusinessHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/unifi-network" 
          element={
            <ProtectedRoute>
              <UnifiNetworkHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/unifi-protect" 
          element={
            <ProtectedRoute>
              <UnifiProtectHelpPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="help/radius" 
          element={
            <ProtectedRoute>
              <RadiusHelpPage />
            </ProtectedRoute>
          } 
        />

        {/* Asset Management routes - Legacy */}
        <Route 
          path="asset-management" 
          element={
            <ProtectedRoute>
              <AssetManagementPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="asset-management/requests" 
          element={
            <ProtectedRoute>
              <AssetRequestsPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="asset-management/issues" 
          element={
            <ProtectedRoute>
              <AssetIssuesPage />
            </ProtectedRoute>
          } 
        />

        {/* New Comprehensive Asset Management routes */}
        <Route 
          path="assets" 
          element={
            <ProtectedRoute>
              <AssetDashboard />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="assets/list" 
          element={
            <ProtectedRoute>
              <AssetListPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="assets/new" 
          element={
            <ProtectedRoute>
              <AssetFormPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="assets/:id" 
          element={
            <ProtectedRoute>
              <AssetFormPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="assets/:id/edit" 
          element={
            <ProtectedRoute>
              <AssetFormPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="assets/reports" 
          element={
            <ProtectedRoute>
              <AssetReportsPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="assets/import-export" 
          element={
            <ProtectedRoute>
              <AssetImportExportPage />
            </ProtectedRoute>
          } 
        />
        {/* Legacy asset management reports route redirect */}
        <Route 
          path="asset-management/reports" 
          element={<Navigate to="/assets/reports" />}
        />

        {/* Notes routes */}
        <Route 
          path="notes" 
          element={
            <ProtectedRoute>
              <NotesPage />
            </ProtectedRoute>
          } 
        />

        {/* News routes */}
        <Route 
          path="news" 
          element={
            <ProtectedRoute>
              <NewsPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="news/posts/new" 
          element={
            <ProtectedRoute>
              <NewsPostForm />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="news/posts/:id" 
          element={
            <ProtectedRoute>
              <NewsPostPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="news/posts/:id/edit" 
          element={
            <ProtectedRoute>
              <NewsPostForm />
            </ProtectedRoute>
          } 
        />

        {/* Form Builder routes */}
        <Route 
          path="forms" 
          element={
            <ProtectedRoute>
              <FormsListPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="forms/new" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <FormBuilderPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="forms/edit/:id" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <FormBuilderPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="forms/view/:slug" 
          element={
            <FormViewPage />
          } 
        />

        {/* Staff Directory routes */}
        <Route 
          path="staff-directory/*" 
          element={
            <ProtectedRoute>
              <StaffDirectoryPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="staff-directory/users/:id" 
          element={
            <ProtectedRoute>
              <UserProfilePage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="staff-directory/teams/new" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <TeamManagementPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="staff-directory/teams/:id" 
          element={
            <ProtectedRoute>
              <TeamManagementPage />
            </ProtectedRoute>
          } 
        />

        <Route 
          path="staff-directory/groups/new" 
          element={
            <ProtectedRoute>
              <GroupManagementPage />
            </ProtectedRoute>
          } 
        />

        <Route 
          path="staff-directory/groups/:id" 
          element={
            <ProtectedRoute>
              <GroupManagementPage />
            </ProtectedRoute>
          } 
        />

        {/* Ticket Management routes */}
        <Route 
          path="tickets" 
          element={
            <ProtectedRoute>
              <TicketsPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="tickets/new" 
          element={
            <ProtectedRoute>
              <TicketFormPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="tickets/:id" 
          element={
            <ProtectedRoute>
              <TicketDetailPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="tickets/:id/edit" 
          element={
            <ProtectedRoute>
              <TicketFormPage />
            </ProtectedRoute>
          } 
        />
        
        {/* Admin Ticket Management routes */}
        <Route 
          path="admin/ticket-categories-and-tags" 
          element={
            <ProtectedRoute requiredRoles={['admin']}>
              <TicketCategoriesAndTagsPage />
            </ProtectedRoute>
          } 
        />
        {/* Redirect old routes to new combined page */}
        <Route 
          path="admin/ticket-categories" 
          element={<Navigate to="/admin/ticket-categories-and-tags" />} 
        />
        <Route 
          path="admin/ticket-tags" 
          element={<Navigate to="/admin/ticket-categories-and-tags" />} 
        />

        {/* Task Management routes */}
        <Route 
          path="tasks/*" 
          element={
            <ProtectedRoute>
              <TasksPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="tasks/new" 
          element={
            <ProtectedRoute>
              <TaskFormPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="tasks/maintenance/new" 
          element={
            <ProtectedRoute>
              <MaintenanceTaskFormPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="tasks/:id" 
          element={
            <ProtectedRoute>
              <TaskDetailPage />
            </ProtectedRoute>
          } 
        />
        <Route 
          path="tasks/:id/edit" 
          element={
            <ProtectedRoute>
              <TaskFormPage />
            </ProtectedRoute>
          } 
        />

        {/* 404 route */}
        <Route 
          path="teams/:teamId/hub" 
          element={
            <ProtectedRoute>
              <TeamHubPage />
            </ProtectedRoute>
          } 
        />
        <Route path="*" element={<NotFoundPage />} />
      </Route>
    </Routes>
    <DebugWidget />
    </DebugProvider>
  );
}

export default App;
