import React, { useState, useCallback, useRef } from 'react';
import { 
  Box, 
  Container, 
  Typography, 
  Grid, 
  Button,
  Divider,
  Paper,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  CircularProgress,
  Alert,
  IconButton,
  Tooltip,
  Menu,
  MenuItem,
  TextField
} from '@mui/material';
import { 
  Add as AddIcon,
  Edit as EditIcon,
  Save as SaveIcon,
  Refresh as RefreshIcon,
  Dashboard as DashboardIcon,
  Settings as SettingsIcon,
  Close as CloseIcon,
  DragIndicator as DragIndicatorIcon,
  AspectRatio as ResizeIcon
} from '@mui/icons-material';
import { DragDropContext, Droppable, Draggable } from '@hello-pangea/dnd';
import { useAuth } from '../context/AuthContext';
import { useDashboard } from '../context/DashboardContext';
import { renderWidget, widgetTypes } from '../components/widgets/WidgetRegistry';
import WidgetSettingsForm from '../components/widgets/WidgetSettingsForm';
import { debounce } from '../utils/performance';

// CSS for responsive widget sizing
const widgetStyles = `
  @media (min-width: 960px) {
    .widget-container.widget-md-12 { width: calc(100% - 24px) !important; }
    .widget-container.widget-md-8 { width: calc(66.666% - 24px) !important; }
    .widget-container.widget-md-6 { width: calc(50% - 24px) !important; }
    .widget-container.widget-md-4 { width: calc(33.333% - 24px) !important; }
    .widget-container.widget-md-3 { width: calc(25% - 24px) !important; }
  }
  
  @media (min-width: 600px) and (max-width: 959px) {
    .widget-container.widget-sm-12 { width: calc(100% - 24px) !important; }
    .widget-container.widget-sm-6 { width: calc(50% - 24px) !important; }
    .widget-container.widget-sm-4 { width: calc(33.333% - 24px) !important; }
    .widget-container.widget-sm-3 { width: calc(25% - 24px) !important; }
  }
  
  @media (max-width: 599px) {
    .widget-container { width: calc(100% - 24px) !important; }
  }
`;

const DashboardPage = () => {
  const { user, hasPermission } = useAuth();
  const { 
    preferences, 
    unsavedPreferences,
    loading, 
    error, 
    isEditMode, 
    hasUnsavedChanges,
    toggleEditMode, 
    addWidget, 
    updateWidget, 
    removeWidget, 
    resetDashboard, 
    updateLayout: originalUpdateLayout,
    saveDashboardChanges,
    discardChanges
  } = useDashboard();

  // Track if a resize operation is in progress
  const [isResizing, setIsResizing] = useState(false);

  // Create a debounced function to reset the resizing flag
  const debouncedResetResizing = useCallback(
    debounce(() => {
      setIsResizing(false);
    }, 500),
    []
  );

  // State for add widget dialog
  const [addWidgetOpen, setAddWidgetOpen] = useState(false);

  // State for widget settings dialog
  const [settingsOpen, setSettingsOpen] = useState(false);
  const [currentWidget, setCurrentWidget] = useState(null);

  // State for dashboard menu
  const [menuAnchorEl, setMenuAnchorEl] = useState(null);
  const menuOpen = Boolean(menuAnchorEl);

  // Handle opening the add widget dialog
  const handleAddWidgetOpen = () => {
    setAddWidgetOpen(true);
  };

  // Handle closing the add widget dialog
  const handleAddWidgetClose = () => {
    setAddWidgetOpen(false);
  };

  // Handle opening the settings dialog
  const handleSettingsOpen = (widget) => {
    setCurrentWidget(widget);
    setWidgetTitle(widget.title || '');
    setWidgetFormSettings(widget.settings || {});
    setSettingsOpen(true);
  };

  // Handle closing the settings dialog
  const handleSettingsClose = () => {
    setSettingsOpen(false);
    setCurrentWidget(null);
    setWidgetTitle('');
    setWidgetFormSettings({});
  };

  // Handle opening the dashboard menu
  const handleMenuOpen = (event) => {
    setMenuAnchorEl(event.currentTarget);
  };

  // Handle closing the dashboard menu
  const handleMenuClose = () => {
    setMenuAnchorEl(null);
  };

  // Handle adding a new widget
  const handleAddWidget = (type) => {
    const widgetType = widgetTypes.find(w => w.type === type);
    if (!widgetType) return;

    // Determine default size based on widget type
    let defaultSize = { w: 6, h: 2 }; // Medium size by default
    
    // Special handling for specific widget types
    if (type === 'shortcuts' || type === 'recentFiles') {
      defaultSize = { w: 6, h: 2 }; // Half width
    } else if (type === 'calendar' || type === 'weather') {
      defaultSize = { w: 4, h: 2 }; // One-third width
    } else if (type === 'notes' || type === 'tasks') {
      defaultSize = { w: 8, h: 3 }; // Two-thirds width, taller
    }

    // Add the widget with default position (will be placed at the end by the flex layout)
    addWidget(
      type, 
      widgetType.title, 
      { x: 0, y: 0, ...defaultSize }
    );

    handleAddWidgetClose();
  };

  // Handle removing a widget
  const handleRemoveWidget = (widgetId) => {
    if (window.confirm('Are you sure you want to remove this widget?')) {
      removeWidget(widgetId);
    }
  };

  // Handle editing a widget
  const handleEditWidget = (widgetId) => {
    // Use unsavedPreferences when in edit mode, otherwise use preferences
    const currentPreferences = isEditMode && unsavedPreferences ? unsavedPreferences : preferences;
    const widget = currentPreferences?.widgets?.find(w => w._id === widgetId);
    if (widget) {
      handleSettingsOpen(widget);
    }
  };

  // State for widget settings form
  const [widgetFormSettings, setWidgetFormSettings] = useState({});
  const [widgetTitle, setWidgetTitle] = useState('');

  // Handle saving widget settings
  const handleSaveSettings = () => {
    if (!currentWidget) return;

    updateWidget(currentWidget._id, {
      title: widgetTitle || currentWidget.title,
      settings: widgetFormSettings
    });

    handleSettingsClose();
  };
  
  // Handle widget resize
  const handleWidgetResize = (widgetId) => {
    // Only allow changes in edit mode and if widgets exist
    if (!isEditMode || !unsavedPreferences?.widgets) return;
    
    // Set resizing flag to true to prevent loading indicator during resize
    setIsResizing(true);
    
    // Find the widget to resize
    const widgetIndex = unsavedPreferences.widgets.findIndex(w => w._id.toString() === widgetId.toString());
    if (widgetIndex === -1) return;
    
    const widget = unsavedPreferences.widgets[widgetIndex];
    
    // Get current size
    let currentSize = 'medium'; // Default
    if (widget.position.w >= 8) {
      currentSize = 'large';
    } else if (widget.position.w >= 4) {
      currentSize = 'medium';
    } else {
      currentSize = 'small';
    }
    
    // Determine next size in the cycle: small -> medium -> large -> small
    let nextSize = 'medium';
    let nextWidth = 6;
    let nextHeight = 2;
    
    if (currentSize === 'small') {
      nextSize = 'medium';
      nextWidth = 6;
      nextHeight = 2;
    } else if (currentSize === 'medium') {
      nextSize = 'large';
      nextWidth = 12;
      nextHeight = 3;
    } else {
      nextSize = 'small';
      nextWidth = 3;
      nextHeight = 2;
    }
    
    // Create updated widget with new size
    const updatedWidget = {
      ...widget,
      position: {
        ...widget.position,
        w: nextWidth,
        h: nextHeight
      }
    };
    
    // Update the widget in the array
    const updatedWidgets = [...unsavedPreferences.widgets];
    updatedWidgets[widgetIndex] = updatedWidget;
    
    // Update layout
    const updatedLayout = updatedWidgets.map(w => ({
      i: w._id.toString(),
      x: w.position.x || 0,
      y: w.position.y || 0,
      w: w.position.w || 4,
      h: w.position.h || 2
    }));
    
    // Update layout immediately to track changes
    originalUpdateLayout(updatedLayout);
    
    // Reset the resizing flag after a delay
    debouncedResetResizing();
  };

  // Handle drag end event from react-beautiful-dnd
  const handleDragEnd = (result) => {
    // Only allow changes in edit mode and if widgets exist
    if (!isEditMode || !unsavedPreferences?.widgets || !result.destination) return;

    // Set resizing flag to true to prevent loading indicator during drag
    setIsResizing(true);

    // Get the source and destination indices
    const sourceIndex = result.source.index;
    const destinationIndex = result.destination.index;

    // Update the order of widgets
    const updatedWidgets = [...unsavedPreferences.widgets];
    
    // Remove the widget from its current position
    const [widget] = updatedWidgets.splice(sourceIndex, 1);
    
    // Insert it at the new position
    updatedWidgets.splice(destinationIndex, 0, widget);
    
    // Update positions for all widgets
    const updatedLayout = updatedWidgets.map((widget, index) => ({
      i: widget._id.toString(),
      x: index % 3, // Simple grid layout: 3 columns
      y: Math.floor(index / 3),
      w: widget.position.w || 4,
      h: widget.position.h || 2
    }));

    // Update layout immediately to track changes
    originalUpdateLayout(updatedLayout);
    
    // Reset the resizing flag after a delay to prevent loading indicator during resize
    debouncedResetResizing();
  };

  // Legacy method for compatibility
  const handleWidgetOrderChange = (widgetId, newOrder) => {
    // Only allow changes in edit mode and if widgets exist
    if (!isEditMode || !unsavedPreferences?.widgets) return;

    // Set resizing flag to true to prevent loading indicator during resize
    setIsResizing(true);

    // Update the order of widgets
    const updatedWidgets = [...unsavedPreferences.widgets];
    
    // Find the widget to move
    const widgetIndex = updatedWidgets.findIndex(w => w._id === widgetId);
    if (widgetIndex === -1) return;
    
    // Remove the widget from its current position
    const [widget] = updatedWidgets.splice(widgetIndex, 1);
    
    // Insert it at the new position
    updatedWidgets.splice(newOrder, 0, widget);
    
    // Update positions for all widgets
    const updatedLayout = updatedWidgets.map((widget, index) => ({
      i: widget._id.toString(),
      x: index % 3, // Simple grid layout: 3 columns
      y: Math.floor(index / 3),
      w: widget.position.w || 4,
      h: widget.position.h || 2
    }));

    // Update layout immediately to track changes
    originalUpdateLayout(updatedLayout);
    
    // Reset the resizing flag after a delay to prevent loading indicator during resize
    debouncedResetResizing();
  };

  // Handle saving dashboard changes
  const handleSaveDashboard = async () => {
    if (hasUnsavedChanges) {
      try {
        await saveDashboardChanges();
        // Pass true to toggleEditMode to skip saving again
        toggleEditMode(true);
      } catch (err) {
        console.error('Error saving dashboard:', err);
        // You might want to show an error message to the user here
        return; // Don't exit edit mode if saving fails
      }
    } else {
      // If no unsaved changes, just toggle edit mode
      toggleEditMode();
    }
  };

  // Handle resetting the dashboard
  const handleResetDashboard = () => {
    if (window.confirm('Are you sure you want to reset your dashboard to default?')) {
      resetDashboard();
      handleMenuClose();
    }
  };

  // Determine grid size for each widget based on its size
  const getWidgetGridSize = (widget) => {
    // Default sizes
    let xs = 12; // Full width on mobile
    let sm = 6;  // Half width on tablets
    let md = 4;  // One-third width on desktops
    
    // Ensure widget.position exists
    const position = widget.position || { w: 4, h: 2 };
    
    // Special handling for specific widget types
    if (widget.type === 'shortcuts' || widget.type === 'recentFiles') {
      // These widgets are always half width on desktop and tablet, full width on mobile
      return { xs: 12, sm: 6, md: 6 };
    }
    
    // Size based on widget's position.w property
    if (position.w >= 10) {
      // Full width widgets
      md = 12; // Full width on desktop
      sm = 12; // Full width on tablet
    } else if (position.w >= 8) {
      // Large widgets (2/3 width or more)
      md = 8; // Two-thirds width on desktop
      sm = 12; // Full width on tablet
    } else if (position.w >= 6) {
      // Medium-large widgets (1/2 width or more)
      md = 6; // Half width on desktop
      sm = 12; // Full width on tablet
    } else if (position.w >= 4) {
      // Medium widgets (1/3 width or more)
      md = 4; // One-third width on desktop
      sm = 6; // Half width on tablet
    } else {
      // Small widgets
      md = 3; // One-fourth width on desktop
      sm = 6; // Half width on tablet
    }
    
    return { xs, sm, md };
  };

  // Permission helper: check if current user can access a given widget type
  const canUseWidgetType = (type) => {
    const def = widgetTypes.find(w => w.type === type);
    const required = def && def.requiredPermission;
    // If no required permission specified, default to allowed
    if (!required) return true;
    // Use AuthContext hasPermission
    return typeof hasPermission === 'function' ? hasPermission(required) : true;
  };

  // Filter widgets in preferences based on permissions
  const filterWidgetsByPermission = (widgets) => {
    if (!Array.isArray(widgets)) return [];
    return widgets.filter((w) => canUseWidgetType(w.type));
  };

  const displayedWidgets = (isEditMode ? filterWidgetsByPermission(unsavedPreferences?.widgets) : filterWidgetsByPermission(preferences?.widgets));

  return (
    <Container maxWidth="lg">
      {/* Add style tag for responsive widget sizing */}
      <style dangerouslySetInnerHTML={{ __html: widgetStyles }} />
      
      <Box sx={{ 
        mb: 4, 
        display: 'flex', 
        justifyContent: 'space-between', 
        alignItems: 'center',
        background: 'rgba(255, 255, 255, 0.95)',
        backdropFilter: 'blur(10px)',
        borderRadius: '12px',
        p: 3,
        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)'
      }}>
        <Box>
          <Typography variant="h4" component="h1" gutterBottom sx={{ color: '#2d3748', fontWeight: 700 }}>
            Welcome, {user?.name}
          </Typography>
          <Typography variant="body1" sx={{ color: '#718096', fontSize: '1.1rem' }}>
            Here's your dashboard with quick access to important resources
          </Typography>
        </Box>
        <Box>
          <Tooltip title={isEditMode ? "Save Layout" : "Customize Dashboard"}>
            <IconButton 
              color={isEditMode ? "primary" : "default"} 
              onClick={isEditMode ? handleSaveDashboard : () => toggleEditMode()}
              sx={{ mr: 1 }}
            >
              {isEditMode ? <SaveIcon /> : <EditIcon />}
            </IconButton>
          </Tooltip>
          <Tooltip title="Dashboard Options">
            <IconButton
              onClick={handleMenuOpen}
              aria-controls={menuOpen ? 'dashboard-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={menuOpen ? 'true' : undefined}
            >
              <DashboardIcon />
            </IconButton>
          </Tooltip>
          <Menu
            id="dashboard-menu"
            anchorEl={menuAnchorEl}
            open={menuOpen}
            onClose={handleMenuClose}
            MenuListProps={{
              'aria-labelledby': 'dashboard-menu-button',
            }}
          >
            <MenuItem onClick={handleResetDashboard}>
              <ListItemIcon>
                <RefreshIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText>Reset Dashboard</ListItemText>
            </MenuItem>
          </Menu>
        </Box>
      </Box>

      {loading && !isResizing ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
          <CircularProgress />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>{error}</Alert>
      ) : displayedWidgets.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            Your dashboard is empty
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            Add widgets to customize your dashboard
          </Typography>
          <Button 
            variant="contained" 
            startIcon={<AddIcon />}
            onClick={handleAddWidgetOpen}
          >
            Add Widget
          </Button>
        </Paper>
      ) : (
        <Box sx={{ position: 'relative', pb: 8 }}>
          {isEditMode ? (
            <DragDropContext onDragEnd={handleDragEnd}>
              <Droppable droppableId="dashboard-widgets" type="WIDGET" direction="horizontal">
                {(provided) => (
                  <div
                    {...provided.droppableProps}
                    ref={provided.innerRef}
                    style={{ display: 'flex', flexWrap: 'wrap', margin: '-12px' }}
                  >
                    {filterWidgetsByPermission(unsavedPreferences?.widgets)?.map((widget, index) => {
                      const gridSize = getWidgetGridSize(widget);
                      
                      return (
                        <Draggable 
                          key={widget._id.toString()} 
                          draggableId={widget._id.toString()} 
                          index={index}
                        >
                          {(provided, snapshot) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              style={{
                                ...provided.draggableProps.style,
                                width: `calc(${100 * (gridSize.md / 12)}% - 24px)`,
                                padding: '12px',
                                boxSizing: 'border-box'
                              }}
                              className={`widget-container 
                                widget-md-${gridSize.md} 
                                widget-sm-${gridSize.sm} 
                                widget-xs-${gridSize.xs}`}
                            >
                              <Paper 
                                sx={{ 
                                  p: 2, 
                                  height: '100%',
                                  transition: 'all 0.3s ease',
                                  boxShadow: snapshot.isDragging ? 4 : 1,
                                  '&:hover': {
                                    boxShadow: 3,
                                    '& .widget-controls': {
                                      opacity: 1
                                    }
                                  }
                                }}
                              >
                                <Box sx={{ position: 'relative' }}>
                                  <Box 
                                    className="widget-controls"
                                    sx={{ 
                                      position: 'absolute', 
                                      top: 0, 
                                      right: 0, 
                                      zIndex: 10,
                                      display: 'flex',
                                      opacity: 0,
                                      transition: 'opacity 0.2s ease'
                                    }}
                                  >
                                    <div {...provided.dragHandleProps}>
                                      <IconButton size="small" title="Drag to move">
                                        <DragIndicatorIcon fontSize="small" />
                                      </IconButton>
                                    </div>
                                    <IconButton 
                                      size="small" 
                                      onClick={() => handleWidgetResize(widget._id)}
                                      title="Click to resize"
                                    >
                                      <ResizeIcon fontSize="small" />
                                    </IconButton>
                                    <IconButton 
                                      size="small" 
                                      onClick={() => handleEditWidget(widget._id)}
                                      title="Edit widget settings"
                                    >
                                      <EditIcon fontSize="small" />
                                    </IconButton>
                                    <IconButton 
                                      size="small" 
                                      onClick={() => handleRemoveWidget(widget._id)}
                                      title="Remove widget"
                                    >
                                      <CloseIcon fontSize="small" />
                                    </IconButton>
                                  </Box>
                                  {renderWidget(widget, handleRemoveWidget, handleEditWidget)}
                                </Box>
                              </Paper>
                            </div>
                          )}
                        </Draggable>
                      );
                    })}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          ) : (
            <Grid container spacing={3}>
              {filterWidgetsByPermission(preferences?.widgets)?.map(widget => {
                const gridSize = getWidgetGridSize(widget);
                
                return (
                  <Grid item xs={gridSize.xs} sm={gridSize.sm} md={gridSize.md} key={widget._id.toString()}>
                    <Paper 
                      sx={{ 
                        p: 2, 
                        height: '100%',
                        transition: 'all 0.3s ease',
                        background: 'rgba(255, 255, 255, 0.95)',
                        backdropFilter: 'blur(10px)',
                        borderRadius: '12px',
                        boxShadow: '0 4px 20px rgba(0, 0, 0, 0.1)',
                        '&:hover': {
                          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
                          transform: 'translateY(-2px)'
                        }
                      }}
                    >
                      <Box sx={{ position: 'relative' }}>
                        {renderWidget(widget, handleRemoveWidget, handleEditWidget)}
                      </Box>
                    </Paper>
                  </Grid>
                );
              })}
            </Grid>
          )}

          {isEditMode && (
            <Tooltip title="Add Widget">
              <Fab 
                color="primary" 
                aria-label="add widget"
                onClick={handleAddWidgetOpen}
                sx={{ position: 'fixed', bottom: 20, right: 20 }}
              >
                <AddIcon />
              </Fab>
            </Tooltip>
          )}
        </Box>
      )}

      {/* Add Widget Dialog */}
      <Dialog 
        open={addWidgetOpen} 
        onClose={handleAddWidgetClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Add Widget
          <IconButton
            aria-label="close"
            onClick={handleAddWidgetClose}
            sx={{
              position: 'absolute',
              right: 8,
              top: 8,
            }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent dividers>
          <List>
            {widgetTypes.filter((wt) => canUseWidgetType(wt.type)).map((widgetType) => (
              <ListItemButton 
                key={widgetType.type}
                onClick={() => handleAddWidget(widgetType.type)}
              >
                <ListItemText 
                  primary={widgetType.title} 
                  secondary={widgetType.description} 
                />
              </ListItemButton>
            ))}
          </List>
        </DialogContent>
      </Dialog>

      {/* Widget Settings Dialog */}
      {currentWidget && (
        <Dialog 
          open={settingsOpen} 
          onClose={handleSettingsClose}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle>
            Widget Settings
            <IconButton
              aria-label="close"
              onClick={handleSettingsClose}
              sx={{
                position: 'absolute',
                right: 8,
                top: 8,
              }}
            >
              <CloseIcon />
            </IconButton>
          </DialogTitle>
          <DialogContent dividers>
            <Typography variant="subtitle1" gutterBottom>
              Title
            </Typography>
            <TextField 
              fullWidth
              margin="normal"
              value={widgetTitle}
              onChange={(e) => setWidgetTitle(e.target.value)}
              placeholder="Widget Title"
              sx={{ mb: 3 }}
            />

            <Divider sx={{ my: 2 }} />
            
            <Typography variant="subtitle1" gutterBottom>
              Settings
            </Typography>
            <WidgetSettingsForm 
              widgetType={currentWidget.type}
              initialSettings={currentWidget.settings}
              onChange={setWidgetFormSettings}
            />
          </DialogContent>
          <DialogActions>
            <Button onClick={handleSettingsClose}>Cancel</Button>
            <Button 
              onClick={handleSaveSettings}
              variant="contained"
            >
              Save
            </Button>
          </DialogActions>
        </Dialog>
      )}
    </Container>
  );
};

export default DashboardPage;
