import React, { useState } from 'react';
import { 
  Card, 
  Card<PERSON>eader, 
  CardContent, 
  IconButton, 
  Menu, 
  MenuItem, 
  Typography,
  Box
} from '@mui/material';
import { 
  MoreVert as MoreVertIcon,
  Close as CloseIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';

const Widget = ({ 
  id, 
  title, 
  onRemove, 
  onEdit, 
  children, 
  settings = {},
  showSettings = false
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const open = Boolean(anchorEl);

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleRemove = () => {
    handleClose();
    if (onRemove) onRemove(id);
  };

  const handleEdit = () => {
    handleClose();
    if (onEdit) onEdit(id);
  };

  return (
    <Card sx={{ width: '100%', height: '100%', display: 'flex', flexDirection: 'column' }}>
      <CardHeader
        action={
          <Box>
            <IconButton
              aria-label="widget menu"
              aria-controls={open ? 'widget-menu' : undefined}
              aria-haspopup="true"
              aria-expanded={open ? 'true' : undefined}
              onClick={handleClick}
            >
              <MoreVertIcon />
            </IconButton>
            <Menu
              id="widget-menu"
              anchorEl={anchorEl}
              open={open}
              onClose={handleClose}
              MenuListProps={{
                'aria-labelledby': 'widget-menu-button',
              }}
            >
              {showSettings && (
                <MenuItem onClick={handleEdit}>
                  <SettingsIcon fontSize="small" sx={{ mr: 1 }} />
                  <Typography variant="inherit">Settings</Typography>
                </MenuItem>
              )}
              <MenuItem onClick={handleRemove}>
                <CloseIcon fontSize="small" sx={{ mr: 1 }} />
                <Typography variant="inherit">Remove</Typography>
              </MenuItem>
            </Menu>
          </Box>
        }
        title={title}
        titleTypographyProps={{ variant: 'h6' }}
      />
      <CardContent sx={{ flexGrow: 1 }}>
        {children}
      </CardContent>
    </Card>
  );
};

export default Widget;
