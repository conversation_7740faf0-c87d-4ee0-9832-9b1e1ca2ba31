import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Paper,
  Typography,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Badge,
  IconButton,
  TextField,
  InputAdornment,
  Divider,
  Fab,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Chip,
  CircularProgress,
  Tooltip,
  Collapse,
  Fade
} from '@mui/material';
import {
  Message as MessageIcon,
  Search as SearchIcon,
  Edit as EditIcon,
  Close as CloseIcon,
  Send as SendIcon,
  Group as GroupIcon,
  Person as PersonIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { useAuth } from '../context/AuthContext';
import messageService from '../services/messageService';
import websocketService from '../services/websocketService';
import MessageConversation from './MessageConversation';

const MessageSidebar = () => {
  const { user } = useAuth();
  const [conversations, setConversations] = useState([]);
  const [selectedConversation, setSelectedConversation] = useState(null);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [newMessageOpen, setNewMessageOpen] = useState(false);
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [searchedUsers, setSearchedUsers] = useState([]);
  const [selectedUsers, setSelectedUsers] = useState([]);
  const [isExpanded, setIsExpanded] = useState(true);
  const [unreadTotal, setUnreadTotal] = useState(0);
  const [searchingUsers, setSearchingUsers] = useState(false);
  const searchTimeoutRef = useRef(null);

  // Load conversations on mount
  useEffect(() => {
    loadConversations();
    
    // Subscribe to messaging events
    websocketService.addEventListener('new_message', handleNewMessage);
    websocketService.addEventListener('messages_read', handleMessagesRead);
    websocketService.addEventListener('message_deleted', handleMessageDeleted);
    
    return () => {
      websocketService.removeEventListener('new_message', handleNewMessage);
      websocketService.removeEventListener('messages_read', handleMessagesRead);
      websocketService.removeEventListener('message_deleted', handleMessageDeleted);
    };
  }, []);

  // Load conversations
  const loadConversations = async () => {
    try {
      setLoading(true);
      const response = await messageService.getConversations();
      if (response.success) {
        setConversations(response.conversations);
        // Calculate total unread
        const total = response.conversations.reduce((sum, conv) => sum + (conv.unreadCount || 0), 0);
        setUnreadTotal(total);
      }
    } catch (error) {
      console.error('Error loading conversations:', error);
    } finally {
      setLoading(false);
    }
  };

  // Handle new message from WebSocket
  const handleNewMessage = (data) => {
    const message = data.data;
    
    // Update conversation list
    setConversations(prev => {
      const updated = [...prev];
      const index = updated.findIndex(c => c._id === message.conversation._id);
      
      if (index >= 0) {
        // Update existing conversation
        updated[index] = {
          ...updated[index],
          lastMessage: message,
          lastMessageAt: message.createdAt,
          unreadCount: (updated[index].unreadCount || 0) + 1
        };
        // Move to top
        const [conv] = updated.splice(index, 1);
        updated.unshift(conv);
      } else {
        // Add new conversation
        updated.unshift({
          ...message.conversation,
          lastMessage: message,
          lastMessageAt: message.createdAt,
          unreadCount: 1
        });
      }
      
      return updated;
    });
    
    // Update unread total
    setUnreadTotal(prev => prev + 1);
  };

  // Handle messages read event
  const handleMessagesRead = (data) => {
    const { conversationId, userId } = data.data;
    
    if (userId === user._id) {
      // Current user read messages
      setConversations(prev => 
        prev.map(conv => 
          conv._id === conversationId 
            ? { ...conv, unreadCount: 0 }
            : conv
        )
      );
      
      // Recalculate total
      setConversations(prev => {
        const total = prev.reduce((sum, conv) => sum + (conv.unreadCount || 0), 0);
        setUnreadTotal(total);
        return prev;
      });
    }
  };

  // Handle message deleted event
  const handleMessageDeleted = (data) => {
    // Refresh conversations to update last message if needed
    loadConversations();
  };

  // Search users for new conversation
  const searchUsers = async (query) => {
    if (!query || query.length < 2) {
      setSearchedUsers([]);
      return;
    }
    
    try {
      setSearchingUsers(true);
      const response = await messageService.searchUsers(query);
      if (response.success) {
        setSearchedUsers(response.users);
      }
    } catch (error) {
      console.error('Error searching users:', error);
    } finally {
      setSearchingUsers(false);
    }
  };

  // Handle user search with debounce
  const handleUserSearch = (query) => {
    setUserSearchQuery(query);
    
    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Set new timeout
    searchTimeoutRef.current = setTimeout(() => {
      searchUsers(query);
    }, 300);
  };

  // Start new conversation
  const startNewConversation = async () => {
    if (selectedUsers.length === 0) return;
    
    try {
      const response = await messageService.getOrCreateConversation(
        selectedUsers.map(u => u._id),
        false
      );
      
      if (response.success) {
        setSelectedConversation(response.conversation);
        setNewMessageOpen(false);
        setSelectedUsers([]);
        setUserSearchQuery('');
        setSearchedUsers([]);
        
        // Add to conversations if new
        if (response.isNew) {
          setConversations(prev => [response.conversation, ...prev]);
        }
      }
    } catch (error) {
      console.error('Error starting conversation:', error);
    }
  };

  // Filter conversations based on search
  const filteredConversations = conversations.filter(conv => {
    if (!searchQuery) return true;
    
    const displayName = messageService.getConversationDisplayName(conv, user._id);
    return displayName.toLowerCase().includes(searchQuery.toLowerCase());
  });

  // Toggle user selection
  const toggleUserSelection = (user) => {
    setSelectedUsers(prev => {
      const exists = prev.find(u => u._id === user._id);
      if (exists) {
        return prev.filter(u => u._id !== user._id);
      }
      return [...prev, user];
    });
  };

  return (
    <>
      {/* Sidebar Container */}
      <Paper
        sx={{
          position: 'fixed',
          right: 20,
          top: '50%',
          transform: 'translateY(-50%)',
          width: isExpanded ? 350 : 60,
          maxHeight: '70vh',
          display: 'flex',
          flexDirection: 'column',
          background: 'rgba(255, 255, 255, 0.95)',
          backdropFilter: 'blur(10px)',
          borderRadius: '12px',
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.15)',
          zIndex: 1200,
          transition: 'width 0.3s ease'
        }}
      >
        {/* Header */}
        <Box
          sx={{
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            borderBottom: '1px solid rgba(0, 0, 0, 0.1)',
            background: 'linear-gradient(135deg, #2563eb, #1e40af)',
            borderRadius: '12px 12px 0 0'
          }}
        >
          {isExpanded ? (
            <>
              <Typography variant="h6" sx={{ color: 'white', fontWeight: 600 }}>
                Messages
              </Typography>
              <Box>
                {unreadTotal > 0 && (
                  <Chip
                    label={unreadTotal}
                    size="small"
                    sx={{
                      backgroundColor: 'rgba(255, 255, 255, 0.2)',
                      color: 'white',
                      mr: 1
                    }}
                  />
                )}
                <IconButton
                  size="small"
                  onClick={() => setIsExpanded(false)}
                  sx={{ color: 'white' }}
                >
                  <ExpandLessIcon />
                </IconButton>
              </Box>
            </>
          ) : (
            <IconButton
              onClick={() => setIsExpanded(true)}
              sx={{ color: 'white', mx: 'auto' }}
            >
              <Badge badgeContent={unreadTotal} color="error">
                <MessageIcon />
              </Badge>
            </IconButton>
          )}
        </Box>

        {/* Content */}
        <Collapse in={isExpanded} timeout="auto">
          <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column', overflow: 'hidden' }}>
            {/* Search Bar */}
            <Box sx={{ p: 2 }}>
              <TextField
                fullWidth
                size="small"
                placeholder="Search conversations..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon fontSize="small" />
                    </InputAdornment>
                  ),
                  sx: {
                    borderRadius: '8px',
                    backgroundColor: 'rgba(0, 0, 0, 0.05)'
                  }
                }}
              />
            </Box>

            <Divider />

            {/* Conversations List */}
            <Box sx={{ flex: 1, overflow: 'auto' }}>
              {loading ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
                  <CircularProgress size={24} />
                </Box>
              ) : filteredConversations.length > 0 ? (
                <List sx={{ p: 0 }}>
                  {filteredConversations.map((conversation) => {
                    const displayName = messageService.getConversationDisplayName(conversation, user._id);
                    const avatar = messageService.getConversationAvatar(conversation, user._id);
                    const isSelected = selectedConversation?._id === conversation._id;
                    
                    return (
                      <ListItem
                        key={conversation._id}
                        button
                        selected={isSelected}
                        onClick={() => setSelectedConversation(conversation)}
                        sx={{
                          '&:hover': {
                            backgroundColor: 'rgba(37, 99, 235, 0.1)'
                          },
                          '&.Mui-selected': {
                            backgroundColor: 'rgba(37, 99, 235, 0.15)',
                            '&:hover': {
                              backgroundColor: 'rgba(37, 99, 235, 0.2)'
                            }
                          }
                        }}
                      >
                        <ListItemAvatar>
                          <Badge
                            badgeContent={conversation.unreadCount}
                            color="error"
                            invisible={!conversation.unreadCount}
                          >
                            <Avatar
                              src={avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(avatar)}` : undefined}
                              sx={{
                                backgroundColor: conversation.isGroup ? '#1e40af' : '#2563eb'
                              }}
                            >
                              {conversation.isGroup ? <GroupIcon /> : displayName[0]?.toUpperCase()}
                            </Avatar>
                          </Badge>
                        </ListItemAvatar>
                        <ListItemText
                          primary={displayName}
                          secondary={
                            conversation.lastMessage
                              ? `${conversation.lastMessage.content.substring(0, 30)}...`
                              : 'Start a conversation'
                          }
                          primaryTypographyProps={{
                            fontWeight: conversation.unreadCount ? 600 : 400,
                            fontSize: '0.95rem'
                          }}
                          secondaryTypographyProps={{
                            fontSize: '0.85rem',
                            sx: {
                              overflow: 'hidden',
                              textOverflow: 'ellipsis',
                              whiteSpace: 'nowrap'
                            }
                          }}
                        />
                        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end' }}>
                          <Typography variant="caption" color="text.secondary">
                            {conversation.lastMessageAt && 
                              messageService.formatMessageTime(conversation.lastMessageAt)
                            }
                          </Typography>
                        </Box>
                      </ListItem>
                    );
                  })}
                </List>
              ) : (
                <Box sx={{ textAlign: 'center', p: 3 }}>
                  <Typography variant="body2" color="text.secondary">
                    No conversations yet
                  </Typography>
                </Box>
              )}
            </Box>

            {/* New Message Button */}
            <Box sx={{ p: 2, borderTop: '1px solid rgba(0, 0, 0, 0.1)' }}>
              <Button
                fullWidth
                variant="contained"
                startIcon={<EditIcon />}
                onClick={() => setNewMessageOpen(true)}
                sx={{
                  background: 'linear-gradient(135deg, #2563eb, #1e40af)',
                  borderRadius: '8px',
                  textTransform: 'none',
                  fontWeight: 600
                }}
              >
                New Message
              </Button>
            </Box>
          </Box>
        </Collapse>
      </Paper>

      {/* Conversation Dialog */}
      {selectedConversation && (
        <MessageConversation
          conversation={selectedConversation}
          open={Boolean(selectedConversation)}
          onClose={() => setSelectedConversation(null)}
          onMessageSent={() => loadConversations()}
        />
      )}

      {/* New Message Dialog */}
      <Dialog
        open={newMessageOpen}
        onClose={() => setNewMessageOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          New Message
          <IconButton
            onClick={() => setNewMessageOpen(false)}
            sx={{ position: 'absolute', right: 8, top: 8 }}
          >
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          <TextField
            fullWidth
            placeholder="Search for users..."
            value={userSearchQuery}
            onChange={(e) => handleUserSearch(e.target.value)}
            sx={{ mb: 2 }}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              )
            }}
          />
          
          {/* Selected Users */}
          {selectedUsers.length > 0 && (
            <Box sx={{ mb: 2 }}>
              {selectedUsers.map(user => (
                <Chip
                  key={user._id}
                  label={user.name}
                  onDelete={() => toggleUserSelection(user)}
                  sx={{ mr: 1, mb: 1 }}
                />
              ))}
            </Box>
          )}
          
          {/* Search Results */}
          {searchingUsers ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <CircularProgress size={24} />
            </Box>
          ) : (
            <List>
              {searchedUsers.map(user => (
                <ListItem
                  key={user._id}
                  button
                  onClick={() => toggleUserSelection(user)}
                  selected={selectedUsers.some(u => u._id === user._id)}
                >
                  <ListItemAvatar>
                    <Avatar src={user.avatar ? `/api/auth/avatar-proxy?url=${encodeURIComponent(user.avatar)}` : undefined}>
                      {user.name[0]}
                    </Avatar>
                  </ListItemAvatar>
                  <ListItemText
                    primary={user.name}
                    secondary={user.email}
                  />
                </ListItem>
              ))}
            </List>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setNewMessageOpen(false)}>Cancel</Button>
          <Button
            onClick={startNewConversation}
            variant="contained"
            disabled={selectedUsers.length === 0}
          >
            Start Conversation
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default MessageSidebar;