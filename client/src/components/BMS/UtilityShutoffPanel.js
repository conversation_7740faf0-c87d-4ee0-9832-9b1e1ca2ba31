import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardHeader,
  CardContent,
  Grid,
  Typography,
  Button,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemButton,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Water as WaterIcon,
  ElectricBolt as ElectricIcon,
  LocalGasStation as GasIcon,
  AcUnit as ACIcon,
  PowerOff as ShutoffIcon,
  Power as PowerOnIcon,
  Warning as WarningIcon,
  LocationOn as LocationIcon,
  Emergency as EmergencyIcon
} from '@mui/icons-material';
import electricalService from '../../services/electricalService';

const UtilityShutoffPanel = ({ buildingId, onHighlightShutoff, onClearHighlight }) => {
  const [shutoffs, setShutoffs] = useState([]);
  const [filteredShutoffs, setFilteredShutoffs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedType, setSelectedType] = useState('all');
  const [emergencyDialogOpen, setEmergencyDialogOpen] = useState(false);
  const [selectedShutoff, setSelectedShutoff] = useState(null);
  const [emergencyReason, setEmergencyReason] = useState('');

  const utilityTypes = [
    { value: 'electrical', label: 'Electrical', icon: <ElectricIcon />, color: 'warning' },
    { value: 'water', label: 'Water', icon: <WaterIcon />, color: 'info' },
    { value: 'gas', label: 'Gas', icon: <GasIcon />, color: 'error' },
    { value: 'hvac', label: 'HVAC', icon: <ACIcon />, color: 'primary' }
  ];

  const emergencyReasons = [
    'Electrical Emergency',
    'Water Leak',
    'Gas Leak',
    'Fire Emergency',
    'Maintenance Work',
    'Equipment Malfunction',
    'Safety Inspection',
    'Other'
  ];

  useEffect(() => {
    fetchShutoffs();
  }, [buildingId]);

  useEffect(() => {
    filterShutoffs();
  }, [shutoffs, selectedType]);

  const fetchShutoffs = async () => {
    try {
      setLoading(true);
      const response = await electricalService.getUtilityShutoffs({ buildingId });
      setShutoffs(response || []);
    } catch (err) {
      console.error('Error fetching utility shutoffs:', err);
      setError('Failed to load utility shutoffs');
    } finally {
      setLoading(false);
    }
  };

  const filterShutoffs = () => {
    let filtered = [...shutoffs];
    
    if (selectedType !== 'all') {
      filtered = filtered.filter(shutoff => shutoff.type === selectedType);
    }
    
    // Sort by emergency status first, then by type
    filtered.sort((a, b) => {
      if (a.isEmergency && !b.isEmergency) return -1;
      if (!a.isEmergency && b.isEmergency) return 1;
      return a.type.localeCompare(b.type);
    });
    
    setFilteredShutoffs(filtered);
  };

  const getUtilityTypeInfo = (type) => {
    return utilityTypes.find(t => t.value === type) || { 
      label: type, 
      icon: <LocationIcon />, 
      color: 'default' 
    };
  };

  const handleShutoffClick = (shutoff) => {
    setSelectedShutoff(shutoff);
    if (onHighlightShutoff) {
      onHighlightShutoff(shutoff);
    }
  };

  const handleEmergencyShutoff = (shutoff) => {
    setSelectedShutoff(shutoff);
    setEmergencyDialogOpen(true);
  };

  const confirmEmergencyShutoff = async () => {
    if (!selectedShutoff) return;

    try {
      await electricalService.performEmergencyShutoff(selectedShutoff._id, {
        reason: emergencyReason,
        timestamp: new Date().toISOString()
      });

      // Refresh the shutoffs list
      await fetchShutoffs();

      setEmergencyDialogOpen(false);
      setSelectedShutoff(null);
      setEmergencyReason('');
    } catch (err) {
      console.error('Error performing emergency shutoff:', err);
      setError('Failed to perform emergency shutoff');
    }
  };

  const handleToggleShutoff = async (shutoff) => {
    try {
      const newStatus = shutoff.status === 'active' ? 'inactive' : 'active';
      await electricalService.updateShutoffStatus(shutoff._id, {
        status: newStatus,
        timestamp: new Date().toISOString()
      });

      // Refresh the shutoffs list
      await fetchShutoffs();
    } catch (err) {
      console.error('Error updating shutoff status:', err);
      setError('Failed to update shutoff status');
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', p: 3 }}>
            <CircularProgress />
            <Typography sx={{ ml: 2 }}>Loading utility shutoffs...</Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader
        title="Utility Shutoffs"
        subheader="Emergency and maintenance shutoff controls"
        avatar={<ShutoffIcon color="error" />}
      />
      <CardContent>
        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}

        {/* Emergency Notice */}
        <Alert severity="error" sx={{ mb: 2 }}>
          <Typography variant="subtitle2" gutterBottom>
            <EmergencyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Emergency Shutoffs
          </Typography>
          <Typography variant="body2">
            Use emergency shutoff buttons only in case of actual emergencies. 
            All shutoff actions are logged and monitored.
          </Typography>
        </Alert>

        {/* Filter */}
        <FormControl fullWidth size="small" sx={{ mb: 2 }}>
          <InputLabel>Utility Type</InputLabel>
          <Select
            value={selectedType}
            label="Utility Type"
            onChange={(e) => setSelectedType(e.target.value)}
          >
            <MenuItem value="all">All Types</MenuItem>
            {utilityTypes.map(type => (
              <MenuItem key={type.value} value={type.value}>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  {type.icon}
                  {type.label}
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {/* Shutoffs List */}
        <List>
          {filteredShutoffs.length > 0 ? (
            filteredShutoffs.map(shutoff => {
              const typeInfo = getUtilityTypeInfo(shutoff.type);
              const isActive = shutoff.status === 'active';
              
              return (
                <ListItem
                  key={shutoff._id}
                  sx={{ 
                    border: shutoff.isEmergency ? '2px solid red' : '1px solid #ddd',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <ListItemButton onClick={() => handleShutoffClick(shutoff)}>
                    <ListItemIcon>
                      <Box sx={{ position: 'relative' }}>
                        {typeInfo.icon}
                        {shutoff.isEmergency && (
                          <EmergencyIcon 
                            sx={{ 
                              position: 'absolute', 
                              top: -4, 
                              right: -4, 
                              fontSize: 12, 
                              color: 'red' 
                            }} 
                          />
                        )}
                      </Box>
                    </ListItemIcon>
                    <ListItemText
                      primary={
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Typography variant="subtitle2">
                            {shutoff.label}
                          </Typography>
                          <Chip
                            label={isActive ? 'ON' : 'OFF'}
                            size="small"
                            color={isActive ? 'success' : 'error'}
                            icon={isActive ? <PowerOnIcon /> : <ShutoffIcon />}
                          />
                          {shutoff.isEmergency && (
                            <Chip
                              label="EMERGENCY"
                              size="small"
                              color="error"
                              variant="filled"
                            />
                          )}
                        </Box>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            {shutoff.location} • {typeInfo.label}
                          </Typography>
                          {shutoff.description && (
                            <Typography variant="caption" color="text.secondary">
                              {shutoff.description}
                            </Typography>
                          )}
                          {shutoff.lastOperation && (
                            <Typography variant="caption" color="text.secondary" display="block">
                              Last operation: {new Date(shutoff.lastOperation).toLocaleString()}
                            </Typography>
                          )}
                        </Box>
                      }
                    />
                  </ListItemButton>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1, ml: 1 }}>
                    <Button
                      variant="contained"
                      color="error"
                      size="small"
                      startIcon={<EmergencyIcon />}
                      onClick={() => handleEmergencyShutoff(shutoff)}
                      disabled={!isActive}
                    >
                      Emergency
                    </Button>
                    <Button
                      variant="outlined"
                      size="small"
                      onClick={() => handleToggleShutoff(shutoff)}
                      startIcon={isActive ? <ShutoffIcon /> : <PowerOnIcon />}
                    >
                      {isActive ? 'Turn Off' : 'Turn On'}
                    </Button>
                  </Box>
                </ListItem>
              );
            })
          ) : (
            <ListItem>
              <ListItemText
                primary="No utility shutoffs found"
                secondary="Contact your system administrator to configure utility shutoffs"
              />
            </ListItem>
          )}
        </List>

        {/* Emergency Shutoff Confirmation Dialog */}
        <Dialog
          open={emergencyDialogOpen}
          onClose={() => setEmergencyDialogOpen(false)}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ color: 'error.main' }}>
            <EmergencyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Emergency Shutoff Confirmation
          </DialogTitle>
          <DialogContent>
            <Alert severity="error" sx={{ mb: 2 }}>
              <Typography variant="subtitle2" gutterBottom>
                WARNING: You are about to perform an emergency shutoff!
              </Typography>
              <Typography variant="body2">
                This will immediately shut off {selectedShutoff?.label} and may affect 
                building operations. This action is logged and monitored.
              </Typography>
            </Alert>
            
            <FormControl fullWidth sx={{ mt: 2 }}>
              <InputLabel>Reason for Emergency Shutoff</InputLabel>
              <Select
                value={emergencyReason}
                label="Reason for Emergency Shutoff"
                onChange={(e) => setEmergencyReason(e.target.value)}
                required
              >
                {emergencyReasons.map(reason => (
                  <MenuItem key={reason} value={reason}>
                    {reason}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </DialogContent>
          <DialogActions>
            <Button onClick={() => setEmergencyDialogOpen(false)}>
              Cancel
            </Button>
            <Button 
              onClick={confirmEmergencyShutoff}
              variant="contained"
              color="error"
              disabled={!emergencyReason}
            >
              Confirm Emergency Shutoff
            </Button>
          </DialogActions>
        </Dialog>
      </CardContent>
    </Card>
  );
};

export default UtilityShutoffPanel;