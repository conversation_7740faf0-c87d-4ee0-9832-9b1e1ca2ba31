/**
 * Middleware to check if user has a specific permission
 */

module.exports = (requiredPermission) => {
  return (req, res, next) => {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({ 
        success: false, 
        message: 'Authentication required' 
      });
    }

    // Admin users with * permission have access to everything
    if (req.user.role && req.user.role.permissions && req.user.role.permissions.includes('*')) {
      return next();
    }

    // Check if user has the specific permission
    if (req.user.role && req.user.role.permissions && req.user.role.permissions.includes(requiredPermission)) {
      return next();
    }

    // Check if user has the permission directly (in case of user-specific permissions)
    if (req.user.permissions && req.user.permissions.includes(requiredPermission)) {
      return next();
    }

    // User doesn't have the required permission
    return res.status(403).json({ 
      success: false, 
      message: `Insufficient permissions. Required: ${requiredPermission}` 
    });
  };
};